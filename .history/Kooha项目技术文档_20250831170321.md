# Kooha 项目技术文档

## 项目概述

Kooha 是一个使用 Rust 语言开发的现代化屏幕录制应用程序，专为 Linux 桌面环境设计。它提供了简洁直观的用户界面，支持多种录制格式，并集成了现代 Linux 桌面技术栈。

### 基本信息
- **项目名称**: Kooha
- **版本**: 2.3.1
- **开发语言**: Rust (2024 edition)
- **许可证**: GPL-3.0-or-later
- **应用ID**: io.github.seadve.Kooha
- **开发者**: <PERSON>o

### 主要特性
- 🎙️ 同时录制麦克风和桌面音频
- 📼 支持 WebM、MP4、GIF 和 Matroska 格式
- 🖥️ 支持选择显示器或屏幕区域录制
- 🛠️ 可配置保存位置、指针可见性、帧率和延迟
- 🚀 实验性硬件加速编码支持

## 技术架构

### 核心技术栈

#### 前端框架
- **GTK4** (>= 4.15.3): 现代化的用户界面框架
- **LibAdwaita** (>= 1.6): GNOME 风格的自适应界面组件
- **GLib/GIO** (>= 2.80): 底层系统集成和异步操作

#### 多媒体处理
- **GStreamer** (>= 1.24): 核心多媒体处理管道
- **GStreamer Plugins Base**: 基础插件集
- **GStreamer Plugins Ugly**: MP4 格式支持
- **GStreamer Plugins Bad**: VA 硬件编码器支持

#### 系统集成
- **PipeWire**: 现代音视频服务器
- **XDG Desktop Portal**: 跨桌面环境的标准化接口
- **D-Bus**: 进程间通信

#### Rust 依赖库
- **gtk4-rs**: GTK4 的 Rust 绑定
- **libadwaita-rs**: LibAdwaita 的 Rust 绑定
- **gstreamer-rs**: GStreamer 的 Rust 绑定
- **anyhow**: 错误处理
- **futures**: 异步编程
- **serde**: 序列化/反序列化
- **tracing**: 日志记录

### 模块架构

```
src/
├── main.rs                 # 应用程序入口点
├── application.rs          # 应用程序主类
├── window/                 # 主窗口模块
│   ├── mod.rs             # 窗口管理
│   ├── progress_icon.rs   # 进度图标
│   └── toggle_button.rs   # 切换按钮
├── recording.rs           # 录制核心逻辑
├── pipeline.rs            # GStreamer 管道构建
├── screencast_portal/     # 屏幕录制门户接口
│   ├── mod.rs            # 门户主模块
│   ├── types.rs          # 类型定义
│   ├── handle_token.rs   # 句柄令牌
│   ├── variant_dict.rs   # 变体字典
│   └── window_identifier.rs # 窗口标识符
├── area_selector/         # 区域选择器
│   ├── mod.rs            # 选择器主模块
│   └── view_port.rs      # 视口管理
├── profile.rs            # 编码配置文件
├── settings.rs           # 应用设置
├── preferences_dialog.rs # 偏好设置对话框
├── device.rs             # 设备管理
├── format.rs             # 格式处理
├── timer.rs              # 计时器
├── experimental.rs       # 实验性功能
├── about.rs              # 关于对话框
├── help.rs               # 帮助功能
├── cancelled.rs          # 取消操作处理
├── item_row.rs           # 列表项行
├── i18n.rs               # 国际化
└── config.rs.in          # 配置模板
```

## 核心模块流程

### 1. 应用程序启动流程

应用程序的启动过程遵循标准的 GTK4 应用程序模式：

1. **初始化阶段**
   - 设置日志系统 (tracing)
   - 配置国际化 (gettext)
   - 初始化 GStreamer 框架
   - 注册自定义 GStreamer 插件

2. **资源加载**
   - 加载 GResource 资源包
   - 注册应用程序图标
   - 设置应用程序元数据

3. **应用程序创建**
   - 创建 Application 实例
   - 设置应用程序 ID 和基础路径
   - 注册全局操作和快捷键

4. **窗口管理**
   - 创建主窗口实例
   - 加载 UI 模板
   - 初始化设置管理器

### 2. 录制核心流程

录制功能是 Kooha 的核心，涉及多个系统组件的协调工作：

#### 2.1 权限和会话管理
- 通过 XDG Desktop Portal 请求屏幕录制权限
- 创建 Screencast 会话并获取会话句柄
- 处理用户的源选择（显示器、窗口或区域）

#### 2.2 媒体管道构建
- 根据用户设置构建 GStreamer 管道
- 配置视频编码器参数（分辨率、帧率、质量）
- 设置音频编码器（如果启用音频录制）
- 连接复用器和文件输出

#### 2.3 实时处理
- 从 PipeWire 接收视频帧数据
- 处理音频流（桌面音频和/或麦克风）
- 实时编码和写入文件
- 更新用户界面状态

### 3. 详细的 GStreamer 管道架构

GStreamer 管道是音视频处理的核心，采用模块化设计：

#### 3.1 视频处理链
```
pipewiresrc → videoscale → videocrop → videoconvert → queue → encoder → muxer
```

- **pipewiresrc**: 从 PipeWire 获取视频流
- **videoscale**: 缩放视频到目标分辨率
- **videocrop**: 裁剪选定区域（区域录制模式）
- **videoconvert**: 颜色空间转换和格式标准化
- **queue**: 缓冲区管理，平滑数据流
- **encoder**: 视频编码（VP8/VP9/AV1/x264/硬件编码器）

#### 3.2 音频处理链
```
audiosrc → audioconvert → encoder → queue → muxer
```

- **audiosrc**: 音频源（PulseAudio/PipeWire）
- **audioconvert**: 音频格式转换
- **encoder**: 音频编码（Opus/MP3/AAC）
- **queue**: 音频缓冲区

#### 3.3 输出处理
- **muxer**: 将音视频流复用到容器格式
- **filesink**: 写入到目标文件

## 构建系统

### Meson 构建配置

项目使用 Meson 作为主要构建系统，配合 Cargo 进行 Rust 代码编译。

#### 主要构建文件
- `meson.build`: 主构建配置
- `src/meson.build`: Rust 代码构建配置
- `data/meson.build`: 资源文件构建配置
- `po/meson.build`: 国际化构建配置

#### 构建依赖
```bash
# 系统依赖
meson >= 0.59
ninja
cargo
appstreamcli
glib-compile-resources
glib-compile-schemas

# 库依赖
glib-2.0 >= 2.80
gio-2.0 >= 2.80
gtk4 >= 4.15.3
libadwaita-1 >= 1.6
gstreamer-1.0 >= 1.24
gstreamer-plugins-base-1.0 >= 1.24
```

#### 构建配置选项
- `profile`: 构建配置文件 (default/development)
- 开发模式: 启用调试信息和实验性功能
- 发布模式: 优化编译，启用 LTO

### Flatpak 打包

项目提供完整的 Flatpak 打包支持，包括：

#### 开发版本配置
- **文件**: `build-aux/io.github.seadve.Kooha.Devel.json`
- **运行时**: org.gnome.Platform (master)
- **SDK**: org.gnome.Sdk + Rust 扩展

#### 权限配置
```json
"finish-args": [
    "--device=dri",              // GPU 访问
    "--filesystem=xdg-videos",   // 视频目录访问
    "--share=ipc",               // IPC 共享
    "--socket=fallback-x11",     // X11 支持
    "--socket=pulseaudio",       // 音频访问
    "--socket=wayland"           // Wayland 支持
]
```

## 开发环境搭建

### 使用 GNOME Builder (推荐)

1. 安装 GNOME Builder
2. 克隆仓库: `https://github.com/SeaDve/Kooha.git`
3. 在 Builder 中打开项目
4. 点击构建按钮

### 使用 Meson 手动构建

```bash
# 克隆项目
git clone https://github.com/SeaDve/Kooha.git
cd Kooha

# 配置构建
meson _build --prefix=/usr/local

# 编译安装
ninja -C _build install
```

### 开发模式构建

```bash
# 开发模式配置
meson _build --prefix=/usr/local -Dprofile=development

# 运行测试
ninja -C _build test
```

## 发布流程

### 版本发布步骤

1. **更新版本号**
   - 修改 `meson.build` 中的版本
   - 修改 `Cargo.toml` 中的版本

2. **更新变更日志**
   - 更新 `src/about.rs` 中的发布说明
   - 更新元数据文件

3. **构建和测试**
   - 运行完整测试套件
   - 验证 Flatpak 构建

4. **发布到 Flathub**
   - 提交到 Flathub 仓库
   - 等待审核和发布

### CI/CD 流程

项目使用 GitHub Actions 进行持续集成：

- **代码检查**: Rust Clippy 静态分析
- **测试**: 单元测试和集成测试
- **构建**: Flatpak 构建验证
- **发布**: 自动发布到 Flathub

## 配置和设置

### 应用设置 (GSettings)

主要配置项存储在 GSettings 中：

```xml
<key name="capture-mode" type="s">
  <choices>
    <choice value="monitor-window"/>
    <choice value="selection"/>
  </choices>
</key>
<key name="record-desktop-audio" type="b"/>
<key name="record-microphone" type="b"/>
<key name="show-pointer" type="b"/>
<key name="record-delay" type="u"/>
<key name="saving-location" type="ay"/>
<key name="profile-id" type="s"/>
<key name="framerate" type="(ii)"/>
```

### 编码配置文件

编码配置存储在 `data/resources/profiles.yml` 中：

- **支持的格式**: WebM (VP8)、MP4、GIF、Matroska
- **实验性格式**: WebM (VP9)、WebM (AV1)、硬件加速编码器

### 实验性功能

通过环境变量 `KOOHA_EXPERIMENTAL` 启用：

- `all`: 启用所有实验性功能
- `experimental-formats`: 实验性编码格式
- `multiple-video-sources`: 多视频源录制
- `window-recording`: 窗口录制

## 国际化支持

项目支持多语言本地化：

- **翻译系统**: GNU gettext
- **翻译平台**: Weblate
- **支持语言**: 40+ 种语言
- **翻译文件**: `po/` 目录下的 `.po` 文件

## 故障排除

### 常见问题

1. **录制不工作**
   - 检查 PipeWire 服务状态
   - 验证 XDG Desktop Portal 安装
   - 查看屏幕录制兼容性

2. **音频问题**
   - 确认 PulseAudio/PipeWire 配置
   - 检查音频设备权限

3. **编码问题**
   - 验证 GStreamer 插件安装
   - 检查硬件编码器支持

### 调试模式

```bash
# 启用调试日志
RUST_LOG=kooha=debug kooha

# Flatpak 调试模式
KOOHA_EXPERIMENTAL=all flatpak run io.github.seadve.Kooha
```

## 贡献指南

### 代码贡献

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 运行测试套件
5. 提交 Pull Request

### 翻译贡献

访问 [Weblate 平台](https://hosted.weblate.org/engage/seadve/) 参与翻译工作。

---

*本文档基于 Kooha 2.3.1 版本编写，如有更新请参考项目官方仓库。*
