# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the kooha package.
# <AUTHOR> <EMAIL>, 2021, 2022.
# <PERSON> <<EMAIL>>, 2021, 2022.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
# <AUTHOR> <EMAIL>, 2021.
# <PERSON> <<EMAIL>>, 2021.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <PERSON> <<EMAIL>>, 2023.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024.
msgid ""
msgstr ""
"Project-Id-Version: kooha\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-23 12:12+0800\n"
"PO-Revision-Date: 2025-08-23 08:45+0000\n"
"Last-Translator: Anonymous <<EMAIL>>\n"
"Language-Team: Chinese (Simplified Han script) <https://hosted.weblate.org/"
"projects/seadve/kooha/zh_Hans/>\n"
"Language: zh_Hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.13\n"

#: data/io.github.seadve.Kooha.desktop.in.in:2
#: data/io.github.seadve.Kooha.metainfo.xml.in.in:7 src/about.rs:24
#: src/main.rs:61
msgid "Kooha"
msgstr "Kooha"

#: data/io.github.seadve.Kooha.desktop.in.in:4
#: data/io.github.seadve.Kooha.metainfo.xml.in.in:8
msgid "Elegantly record your screen"
msgstr "优雅地录制屏幕"

#. Translators: These are search terms to find this application. Do NOT translate or localize the semicolons. The list MUST also end with a semicolon.
#: data/io.github.seadve.Kooha.desktop.in.in:9
msgid "Screencast;Recorder;Screen;Video;"
msgstr "Screencast;Recorder;Screen;Video;录屏;录制;视频;屏幕;屏幕录制;"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:10
msgid ""
"Capture your screen in a intuitive and straightforward way without "
"distractions."
msgstr "以直观、直接的方式捕捉屏幕，无需分心。"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:11
msgid ""
"Kooha is a simple screen recorder with a minimalist interface. You can just "
"click the record button without having to configure a bunch of settings."
msgstr ""
"Kooha 是一个界面极简的录屏软件。无需一系列的配置，只需轻点一下即可开始录制。"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:13
msgid "The main features of Kooha include the following:"
msgstr "Kooha 的主要特性有："

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:15
msgid "🎙️ Record microphone, desktop audio, or both at the same time"
msgstr "🎙️ 可以同时录制麦克风，电脑音频"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:16
msgid "📼 Support for WebM, MP4, GIF, and Matroska formats"
msgstr "📼 支持MP4、GIF、WebM、MKV、MKA和MKS格式"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:17
msgid "🖥️ Select a monitor or a portion of the screen to record"
msgstr "🖥️选择显示器或者屏幕的一块区域进行录制"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:18
msgid ""
"🛠️ Configurable saving location, pointer visibility, frame rate, and delay"
msgstr "🛠️ 你可以配置光标是否可见，帧率的大小和延迟的高低，将配置文件保存在本地"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:19
msgid "🚀 Experimental hardware accelerated encoding"
msgstr "🚀 尝试使用硬件加速编码"

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:25
msgid "Recording options and button to start recording"
msgstr ""

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:29
msgid "In-progress recording duration and button to stop recording"
msgstr ""

#: data/io.github.seadve.Kooha.metainfo.xml.in.in:33
msgid "Countdown to start recording and button to cancel recording"
msgstr ""

#: data/resources/ui/area_selector.ui:15
msgid "Select Area"
msgstr "选择区域"

#: data/resources/ui/area_selector.ui:20 data/resources/ui/window.ui:235
#: data/resources/ui/window.ui:278 src/window/mod.rs:213
msgid "Cancel"
msgstr "取消"

#: data/resources/ui/area_selector.ui:26
msgid "Done"
msgstr "完成"

#: data/resources/ui/area_selector.ui:35
msgid "Reset Selection"
msgstr "重设选区"

#: data/resources/ui/preferences_dialog.ui:10
msgid "General"
msgstr "通用"

#: data/resources/ui/preferences_dialog.ui:13
msgid "Delay (Seconds)"
msgstr "延迟（s）"

#: data/resources/ui/preferences_dialog.ui:14
msgid "Time interval before recording begins"
msgstr "开始录制之前的时间间隔"

#: data/resources/ui/preferences_dialog.ui:27
msgid "Recordings Folder"
msgstr "录制文件夹"

#: data/resources/ui/preferences_dialog.ui:28
msgid "Destination folder for the recordings"
msgstr "录制的目标文件夹"

#: data/resources/ui/preferences_dialog.ui:50
msgid "Video"
msgstr "视频"

#: data/resources/ui/preferences_dialog.ui:53
msgid "Format"
msgstr "格式"

#: data/resources/ui/preferences_dialog.ui:58
msgid "Frame Rate"
msgstr "帧率"

#: data/resources/ui/shortcuts.ui:10
#, fuzzy
msgctxt "shortcut window"
msgid "General"
msgstr "通用"

#: data/resources/ui/shortcuts.ui:13
#, fuzzy
msgctxt "shortcut window"
msgid "Open Menu"
msgstr "打开菜单"

#: data/resources/ui/shortcuts.ui:19
#, fuzzy
msgctxt "shortcut window"
msgid "Show Preferences"
msgstr "显示首选项"

#: data/resources/ui/shortcuts.ui:25
#, fuzzy
msgctxt "shortcut window"
msgid "Show Shortcuts"
msgstr "显示快捷键"

#: data/resources/ui/shortcuts.ui:31
msgctxt "shortcut window"
msgid "Close Window"
msgstr ""

#: data/resources/ui/shortcuts.ui:37
#, fuzzy
msgctxt "shortcut window"
msgid "Quit"
msgstr "退出"

#: data/resources/ui/shortcuts.ui:45
msgctxt "shortcut window"
msgid "Recording"
msgstr ""

#: data/resources/ui/shortcuts.ui:48
msgctxt "shortcut window"
msgid "Toggle Record"
msgstr ""

#: data/resources/ui/shortcuts.ui:55
msgctxt "shortcut window"
msgid "Toggle Pause"
msgstr ""

#: data/resources/ui/shortcuts.ui:61
msgctxt "shortcut window"
msgid "Cancel Record"
msgstr ""

#: data/resources/ui/shortcuts.ui:69
msgctxt "shortcut window"
msgid "Settings"
msgstr ""

#: data/resources/ui/shortcuts.ui:72
msgctxt "shortcut window"
msgid "Toggle Desktop Audio"
msgstr ""

#: data/resources/ui/shortcuts.ui:78
msgctxt "shortcut window"
msgid "Toggle Microphone"
msgstr ""

#: data/resources/ui/shortcuts.ui:84
msgctxt "shortcut window"
msgid "Toggle Pointer"
msgstr ""

#: data/resources/ui/window.ui:26
msgid "Main Menu"
msgstr "主菜单"

#: data/resources/ui/window.ui:55
msgid "Capture a Monitor or Window"
msgstr "捕捉一个显示器或窗口"

#: data/resources/ui/window.ui:68
msgid "Capture a Selection of Screen"
msgstr "捕捉一个屏幕选区"

#: data/resources/ui/window.ui:88
msgid "Enable Desktop Audio"
msgstr "打开桌面音频"

#: data/resources/ui/window.ui:89
msgid "Disable Desktop Audio"
msgstr "关闭桌面音频"

#: data/resources/ui/window.ui:97
msgid "Enable Microphone"
msgstr "打开麦克风"

#: data/resources/ui/window.ui:98
msgid "Disable Microphone"
msgstr "关闭麦克风"

#: data/resources/ui/window.ui:106
msgid "Show Pointer"
msgstr "显示鼠标指针"

#: data/resources/ui/window.ui:107
msgid "Hide Pointer"
msgstr "隐藏鼠标指针"

#: data/resources/ui/window.ui:117
msgid "Start Recording"
msgstr "开始录制"

#: data/resources/ui/window.ui:118
msgid "Record"
msgstr "录制"

#: data/resources/ui/window.ui:131
msgid "Forget Previously Selected Video Sources"
msgstr "重设原先的视频源"

#: data/resources/ui/window.ui:182
msgid "Stop Recording"
msgstr "停止记录"

#: data/resources/ui/window.ui:183
msgid "Stop"
msgstr "停止"

#: data/resources/ui/window.ui:193
msgid "Pause Recording"
msgstr "停止录制"

#: data/resources/ui/window.ui:216
msgid "Recording in…"
msgstr "即将录制……"

#: data/resources/ui/window.ui:234 data/resources/ui/window.ui:277
msgid "Cancel Recording"
msgstr "取消录制"

#: data/resources/ui/window.ui:259
msgid "Flushing…"
msgstr "冲制中…"

#: data/resources/ui/window.ui:295
msgid "_Preferences"
msgstr "设置 (_P)"

#: data/resources/ui/window.ui:299
msgid "_Keyboard Shortcuts"
msgstr "快捷键(_K)"

#: data/resources/ui/window.ui:303
msgid "_About Kooha"
msgstr "关于 Kooha (_A)"

#. Translators: Replace "translator-credits" with your names. Put a comma between.
#: src/about.rs:35
msgid "translator-credits"
msgstr ""
"有趣的灵魂 (@TheInterestingSoul)\n"
"Zhou Nan &lt;<EMAIL>&gt;\n"
"Zhongrui Cao &lt;<EMAIL>&gt;\n"
"WhiredPlanck &lt;<EMAIL>&gt;\n"
"lumingzh &lt;<EMAIL>&gt;"

#: src/about.rs:45
msgid "Donate (Buy Me a Coffee)"
msgstr "捐赠（买我一杯咖啡！）"

#: src/about.rs:48
msgid "GitHub"
msgstr "GitHub"

#: src/about.rs:50
msgid "Translate"
msgstr "翻译"

#. Translators: This is a message that the user will see when the recording is finished.
#: src/application.rs:137
msgid "Screencast recorded"
msgstr "截屏记录"

#: src/application.rs:144
msgid "Show in Files"
msgstr "在文件中显示"

#: src/device.rs:26
msgid "Failed to find the default audio device"
msgstr "寻找默认音频设备失败"

#: src/device.rs:27
msgid "Make sure that you have PulseAudio installed in your system."
msgstr "确保你已在系统安装PulseAudio。"

#. Translators: Do NOT translate the contents between '{' and '}', this is a variable name.
#: src/format.rs:36
msgid "{time} hour"
msgid_plural "{time} hours"
msgstr[0] "{time} 时"

#. Translators: Do NOT translate the contents between '{' and '}', this is a variable name.
#: src/format.rs:43
msgid "{time} minute"
msgid_plural "{time} minutes"
msgstr[0] "{time} 分"

#. Translators: Do NOT translate the contents between '{' and '}', this is a variable name.
#: src/format.rs:50
msgid "{time} second"
msgid_plural "{time} seconds"
msgstr[0] "{time} 秒"

#: src/preferences_dialog.rs:76
msgid "Failed to set recordings folder"
msgstr "设置录制文件夹失败"

#: src/preferences_dialog.rs:236
msgid "This frame rate may cause performance issues on the selected format."
msgstr "在已选择的编码格式下此帧率可能会造成性能问题。"

#: src/preferences_dialog.rs:300
msgid "This format is experimental and unsupported."
msgstr "此格式是实验性的，不受支持。"

#: src/preferences_dialog.rs:307 src/window/mod.rs:557
msgid "None"
msgstr "无"

#: src/recording.rs:42
msgid "No active profile"
msgstr "配置无效"

#: src/recording.rs:187 src/recording.rs:226 src/recording.rs:276
msgid "Failed to start recording"
msgstr "开始录制失败"

#. Translators: Do NOT translate the contents between '{' and '}', this is a variable name.
#: src/recording.rs:190
msgid "Check out {link} for help."
msgstr "查看 “{link}” 以获取帮助。"

#: src/recording.rs:227
msgid "A GStreamer plugin may not be installed."
msgstr "GStreamer plugin可能没有安装。"

#: src/recording.rs:277 src/recording.rs:506
msgid "Make sure that the saving location exists and is accessible."
msgstr "确保保存路径存在并可以访问。"

#: src/recording.rs:497
msgid "An error occurred while recording"
msgstr "录制时产生错误"

#. Translators: Do NOT translate the contents between '{' and '}', this is a variable name.
#: src/recording.rs:503
msgid "Failed to open “{path}” for writing"
msgstr "无法打开并写入 “{path}”"

#: src/settings.rs:43
msgid "Select Recordings Folder"
msgstr "选择录制保存文件夹"

#: src/window/mod.rs:78
msgid "Failed to toggle pause"
msgstr "切换暂停状态失败"

#: src/window/mod.rs:199
msgid ""
"A recording is currently in progress. Quitting immediately may cause the "
"recording to be unplayable. Please stop the recording before quitting."
msgstr ""
"当前一个录制任务正在进行。 立即退出可能造成录制的文件无法播放。 请在退出前停"
"止录制。"

#: src/window/mod.rs:202
msgid ""
"Quitting will cancel the processing and may cause the recording to be "
"unplayable."
msgstr "退出会取消此正在处理的任务且可能导致录制的文件无法播放。"

#: src/window/mod.rs:208
msgid "Quit the Application?"
msgstr "退出此应用？"

#: src/window/mod.rs:215
msgid "Quit"
msgstr "退出"

#: src/window/mod.rs:256
msgid "Copy to clipboard"
msgstr "复制到剪贴板"

#: src/window/mod.rs:261
msgid "Copied to clipboard"
msgstr "已复制到剪贴板"

#: src/window/mod.rs:267
msgid "Show detailed error"
msgstr "显示错误详情"

#: src/window/mod.rs:289
msgid "Help"
msgstr "帮助"

#: src/window/mod.rs:294
msgid "Ok, Got It"
msgstr "好的，明白了"

#: src/window/mod.rs:303
msgid "Open Preferences?"
msgstr "是否要打开设置？"

#: src/window/mod.rs:304
msgid ""
"The previously selected format may have been unavailable. Open preferences "
"and select a format to continue recording."
msgstr "先前选择的格式可能不可用。打开首选项并选择一种格式以继续录制。"

#: src/window/mod.rs:308
msgid "Later"
msgstr "稍后"

#: src/window/mod.rs:310
msgid "Open"
msgstr "打开"

#: src/window/mod.rs:461
msgid "A recording is in progress"
msgstr "一个录制任务正在进行"

#: src/window/mod.rs:499
msgid "Recording"
msgstr "录制中"

#: src/window/mod.rs:507
msgid "Paused"
msgstr "已暂停"

#: src/window/mod.rs:544
msgid "Normal"
msgstr "一般"

#: src/window/mod.rs:545
msgid "Selection"
msgstr "选区录制"

#~ msgid "Open Menu"
#~ msgstr "打开菜单"

#~ msgid "Show Preferences"
#~ msgstr "显示设置"

#~ msgid "Show Shortcuts"
#~ msgstr "显示快捷键"

#~ msgid "Toggle Record"
#~ msgstr "录制/停止"

#~ msgid "Toggle Pause"
#~ msgstr "继续/暂停"

#~ msgid "Cancel Record"
#~ msgstr "取消录制"

#~ msgid "Settings"
#~ msgstr "设置"

#~ msgid "Toggle Desktop Audio"
#~ msgstr "打开/关闭电脑音频"

#~ msgid "Toggle Microphone"
#~ msgstr "打开/关闭麦克风"

#~ msgid "Toggle Pointer"
#~ msgstr "显示/隐藏鼠标指针"

#~ msgid "Failed to start device monitor"
#~ msgstr "启动设备监视器失败"

#~ msgid "Failed to connect to PulseAudio daemon"
#~ msgstr "连接到PulseAudio守护进程失败"

#~ msgid "No microphone source found"
#~ msgstr "没有找到麦克风源"

#~ msgid "No desktop speaker source found"
#~ msgstr "没有找到桌面音频源"

#~ msgid "Ok"
#~ msgstr "Ok"

#~ msgid "This frame rate may cause performance issues."
#~ msgstr "此帧率可能会导致性能问题。"

#~ msgid "approx."
#~ msgstr "大约。"

#~ msgid "🖥️ Select a monitor, a window, or a portion of the screen to record"
#~ msgstr "🖥️ 你可以录制桌面，窗口或者屏幕的指定部分"

#~ msgid "🗔 Multiple sources selection"
#~ msgstr "🗔 你可以同时启用多个录制源"

#~ msgid "Dave Patrick Caberto"
#~ msgstr "Dave Patrick Caberto"

#~ msgid "© 2022 Dave Patrick Caberto"
#~ msgstr "© 2022 Dave Patrick Caberto"

#~ msgid "Click here to view the video."
#~ msgstr "点击此处查看视频。"

#~ msgid "Failed to select saving location"
#~ msgstr "选择保存位置失败"

#~ msgid "GIF"
#~ msgstr "GIF"

#~ msgid "WebM"
#~ msgstr "WebM"

#~ msgid "MP4"
#~ msgstr "MP4"

#~ msgid "Matroska"
#~ msgstr "Matroska"

#~ msgid "WebM VP9"
#~ msgstr "WebM VP9"

#~ msgid "WebM AV1"
#~ msgstr "WebM AV1"

#~ msgid "WebM VAAPI VP8"
#~ msgstr "WebM VAAPI VP8"

#~ msgid "WebM VAAPI VP9"
#~ msgstr "WebM VAAPI VP9"

#~ msgid "WebM VAAPI H264"
#~ msgstr "WebM VAAPI H264"

#~ msgid "Failed to open file for writing"
#~ msgstr "打开文件写入失败"

#~ msgid "Donate (Liberapay)"
#~ msgstr "捐赠（Liberapay）"

#~ msgid "Donate (PayPal)"
#~ msgstr "捐赠（PayPal）"

#~ msgid "Check out {} for help."
#~ msgstr "查看{}以获取帮助。"

#~ msgid "Failed to open “{}” for writing"
#~ msgstr "打开“{}”写入失败"

#~ msgid "_Cancel"
#~ msgstr "取消(_C)"

#~ msgid "_Select"
#~ msgstr "选择(_S)"

#~ msgid "No folder selected"
#~ msgstr "没有选中文件夹"

#~ msgid "Please choose a folder and try again."
#~ msgstr "请选择一个文件夹，然后重试。"

#~ msgid "Cannot access “{}”"
#~ msgstr "无法访问“{}”"

#~ msgid "Please choose an accessible location and try again."
#~ msgstr "请选择一个可以访问的位置并重试。"

#~ msgid "🎥 Capture your screen without any hassle."
#~ msgstr "🎥 轻松录屏，省心省时。"

#~ msgid "🎙️ Record your microphone, computer sounds, or both at the same time."
#~ msgstr "🎙️ 录制麦克风或计算机声音，抑或两者同时录制。"

#~ msgid "📼 Support for WebM, MP4, GIF, and MKV formats."
#~ msgstr "📼 支持 WebM、MP4、GIF 和 MKV 格式。"

#~ msgid "🗔 Multiple sources selection."
#~ msgstr "🗔 多个画面来源选择。"

#~ msgid "🚀 Optional hardware accelerated encoding"
#~ msgstr "🚀 可选硬件加速编码"

#~ msgid "🖥️ Select a monitor or window to record."
#~ msgstr "🖥️ 选择显示器或窗口以录制。"

#~ msgid "🖼️ Create a selection to capture certain area from your screen."
#~ msgstr "🖼️ 在屏幕上选择特定区域来捕获画面。"

#~ msgid "⏲️ Set delay to prepare before you start recording."
#~ msgstr "⏲️ 在开始录制之前设置延迟来准备。"

#~ msgid "🖱️ Hide or show mouse pointer."
#~ msgstr "🖱️ 显示/隐藏鼠标指针。"

#~ msgid "💾 Choose a saving location for your recording."
#~ msgstr "💾 为您的录像选择一个保存位置。"

#~ msgid "⌨️ Utilize helpful keyboard shortcuts."
#~ msgstr "⌨️ 键盘快捷键让你事半功倍。"

#~ msgid "Toggle Computer Sounds"
#~ msgstr "切换电脑声音录制状态"

#~ msgid "_Delay"
#~ msgstr "延迟(_D)"

#~ msgid "3 seconds"
#~ msgstr "3秒"

#~ msgid "5 seconds"
#~ msgstr "5 秒"

#~ msgid "10 seconds"
#~ msgstr "10 秒"

#~ msgid "_Video Format"
#~ msgstr "视频格式(_V)"

#~ msgid "MKV"
#~ msgstr "MKV"

#~ msgid "_Save to…"
#~ msgstr "保存到(_S)…"

#~ msgid ""
#~ "A GStreamer plugin may not be installed. If it is installed but still "
#~ "does not work properly, please report to <a href=\"https://github.com/"
#~ "SeaDve/Kooha/issues\">Kooha's issue page</a>."
#~ msgstr ""
#~ "可能未安装 GStreamer 插件。如果已安装但仍无法正常工作，请报告<a "
#~ "href=\"https://github.com/SeaDve/Kooha/issues\">kooha问题页面</a>。"

#~ msgid "Record Computer Sounds"
#~ msgstr "录制本机声音"

#~ msgid "Record From Microphone"
#~ msgstr "录制麦克风声音"

#~ msgid "Dave Patrick"
#~ msgstr "Dave Patrick"

#~ msgid "Cancel Delay"
#~ msgstr "取消延迟"

#~ msgid "Copyright 2021 Dave Patrick"
#~ msgstr "版权所有: Dave Patrick"

#~ msgid "Screencast Recorded!"
#~ msgstr "屏幕已录制！"

#~ msgid "The recording has been saved in “{}”"
#~ msgstr "录制文件已保存到“{}”"

#~ msgid "Open File"
#~ msgstr "打开文件"

#~ msgid "Screencast Portal Request Error"
#~ msgstr "请求录制入口时出错"

#~ msgid "Pipeline Build Error"
#~ msgstr "管道构建时出错"

#~ msgid "Recording Error"
#~ msgstr "录制时出错"

#~ msgid ""
#~ "Make sure to check for the runtime dependencies and <a href=\"https://"
#~ "github.com/SeaDve/Kooha#-it-doesnt-work\">It Doesn't Work page</a>."
#~ msgstr ""
#~ "请确保检查运行时依赖和<a href=\"https://github.com/SeaDve/Kooha#-it-"
#~ "doesnt-work\">“为何不工作”说明页</a>。"

#~ msgid ""
#~ "Make sure that the saving location exists or is accessible. If it "
#~ "actually exists or is accessible, something went wrong and please report "
#~ "to <a href=\"https://github.com/SeaDve/Kooha/issues\">Kooha's issue page</"
#~ "a>."
#~ msgstr ""
#~ "‎确保保存位置存在或可访问。如果确实存在且可访问，则出问题，请报告给‎‎<a "
#~ "href=\"https://github.com/SeaDve/Kooha/issues\">‎‎古嗣的问题页面‎‎</a>‎。"

#~ msgid ""
#~ "A GStreamer plugin may not be installed. If not, please report to <a "
#~ "href=\"https://github.com/SeaDve/Kooha/issues\">Kooha's issue page</a>."
#~ msgstr ""
#~ "GStreamer 插件或许未安装。如果不是这样，请将此报告到 <a href=\"https://"
#~ "github.com/SeaDve/Kooha/issues\">Kooha 的 Issue 页处</a>。"

#~ msgid ""
#~ "Make sure that the saving location exists. If not, something went wrong "
#~ "and please report to <a href=\"https://github.com/SeaDve/Kooha/"
#~ "issues\">Kooha's issue page</a>."
#~ msgstr ""
#~ "请确保保存的位置存在。如果位置确实存在，说明有故障，请将此报告到 <a "
#~ "href=\"https://github.com/SeaDve/Kooha/issues\">Kooha 的 Issue 页处</a>。"

#~ msgid ""
#~ "Capture your screen in a straightforward and painless way without "
#~ "distractions."
#~ msgstr "以直截了当、无痛无扰的方式录制你的屏幕。"

#~ msgid "Please choose an accessible location and retry."
#~ msgstr "请选择一个可访问的位置，然后重试。"

#~ msgid "Select a Folder"
#~ msgstr "选择一个文件夹"

#~ msgid "🖼️ Select an area of your screen to record."
#~ msgstr "🖼️ 选择屏幕中要录制的区域。"

#~ msgid "📼 Support for the free Matroska and WebM formats."
#~ msgstr "📼 支持 MKV 与 WebM 格式。"

#~ msgid "Open menu"
#~ msgstr "打开菜单"

#~ msgid "Show keyboard shortcuts"
#~ msgstr "显示键盘快捷键"

#~ msgid "Toggle record"
#~ msgstr "开始/结束录制"

#~ msgid "Toggle record pause"
#~ msgstr "暂停/继续录制"

#~ msgid "Cancel delay"
#~ msgstr "取消延迟"

#~ msgid "Toggle computer sounds"
#~ msgstr "切换本机声音录制"

#~ msgid "Toggle microphone"
#~ msgstr "切换麦克风录制"

#~ msgid "Toggle pointer"
#~ msgstr "切换鼠标指针显示"

#~ msgid "Delay"
#~ msgstr "延迟"

#~ msgid "Video Format"
#~ msgstr "视频格式"

#~ msgid "Save to…"
#~ msgstr "保存至……"

#~ msgid "Keyboard Shortcuts"
#~ msgstr "快捷键"

#~ msgid "About Kooha"
#~ msgstr "关于 Kooha"

#~ msgid "Sorry! An error has occured."
#~ msgstr "抱歉！软件出现了问题。"

#~ msgid "Videos"
#~ msgstr "视频"

#~ msgid "Failed to create session."
#~ msgstr "无法创建会话。"

#~ msgid "Failed to select sources."
#~ msgstr "无法选择录制源。"

#~ msgid "Select"
#~ msgstr "选择"

#~ msgid "Inaccessible location “{directory}”"
#~ msgstr "无法访问 “{directory}”"

#~ msgid "The recording has been saved in {saving_location}"
#~ msgstr "录像已保存至 {saving_location}"

#~ msgid "Inaccessible location '{directory}'"
#~ msgstr "无法访问位置 '{directory}'"

#~ msgid "Toggle capture mode"
#~ msgstr "切换画面捕获模式"

#~ msgid "Full Screen"
#~ msgstr "全屏录制"

#~ msgid "Processing"
#~ msgstr "处理中"

#~ msgid "Save location not set"
#~ msgstr "未设置保存位置"

#~ msgid "Recording cannot start"
#~ msgstr "无法开始录制"

#~ msgid "The saving location you have selected may have been deleted."
#~ msgstr "您所选择的保存位置可能已被删除。"

#~ msgid "The recording has been saved in"
#~ msgstr "录屏视频已保存到"

#~ msgid "Record computer sounds"
#~ msgstr "录制本机声音"

#~ msgid "Record from microphone"
#~ msgstr "录制麦克风声音"

#~ msgid "Show pointer"
#~ msgstr "显示鼠标指针"

#~ msgid "Elegantly record your screen "
#~ msgstr "优雅地录制你的屏幕。 "

#~ msgid "Simple screen recorder"
#~ msgstr "简单的屏幕录制工具"

#~ msgid ""
#~ "Kooha is a simple screen recorder built with GTK. It allows you to record "
#~ "your screen and also audio from your microphone or desktop."
#~ msgstr ""
#~ "Kooha 是一个使用 GTK 构建的简单的屏幕录制工具。它可以录制你的屏幕和来自你"
#~ "的麦克风或计算机内的声音。"
