<Project xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:foaf="http://xmlns.com/foaf/0.1/"
         xmlns:gnome="http://api.gnome.org/doap-extensions#"
         xmlns="http://usefulinc.com/ns/doap#">

  <name xml:lang="en">Kooha</name>
  <shortdesc xml:lang="en">Elegantly record your screen</shortdesc>
  <homepage rdf:resource="https://github.com/SeaDve/Kooha" />
  <bug-database rdf:resource="https://github.com/SeaDve/Kooha/issues"/>
  <programming-language>Rust</programming-language>

  <maintainer>
    <foaf:Person>
      <foaf:name><PERSON></foaf:name>
      <foaf:mbox rdf:resource="mailto:<EMAIL>" />
      <foaf:account>
        <foaf:OnlineAccount>
            <foaf:accountServiceHomepage rdf:resource="https://github.com/"/>
            <foaf:accountName>SeaDve</foaf:accountName>
        </foaf:OnlineAccount>
      </foaf:account>
    </foaf:Person>
  </maintainer>
</Project>
