# Kooha 项目技术文档

## 项目概述

Kooha 是一个使用 Rust 语言开发的现代化屏幕录制应用程序，专为 Linux 桌面环境设计。它提供了简洁直观的用户界面，支持多种录制格式，并集成了现代 Linux 桌面技术栈。

### 基本信息
- **项目名称**: Kooha
- **版本**: 2.3.1
- **开发语言**: Rust (2024 edition)
- **许可证**: GPL-3.0-or-later
- **应用ID**: io.github.seadve.Kooha
- **开发者**: <PERSON>o

### 主要特性
- 🎙️ 同时录制麦克风和桌面音频
- 📼 支持 WebM、MP4、GIF 和 Matroska 格式
- 🖥️ 支持选择显示器或屏幕区域录制
- 🛠️ 可配置保存位置、指针可见性、帧率和延迟
- 🚀 实验性硬件加速编码支持

## 技术架构

### 核心技术栈

#### 前端框架
- **GTK4** (>= 4.15.3): 现代化的用户界面框架
- **LibAdwaita** (>= 1.6): GNOME 风格的自适应界面组件
- **GLib/GIO** (>= 2.80): 底层系统集成和异步操作

#### 多媒体处理
- **GStreamer** (>= 1.24): 核心多媒体处理管道
- **GStreamer Plugins Base**: 基础插件集
- **GStreamer Plugins Ugly**: MP4 格式支持
- **GStreamer Plugins Bad**: VA 硬件编码器支持

#### 系统集成
- **PipeWire**: 现代音视频服务器
- **XDG Desktop Portal**: 跨桌面环境的标准化接口
- **D-Bus**: 进程间通信

#### Rust 依赖库
- **gtk4-rs**: GTK4 的 Rust 绑定
- **libadwaita-rs**: LibAdwaita 的 Rust 绑定
- **gstreamer-rs**: GStreamer 的 Rust 绑定
- **anyhow**: 错误处理
- **futures**: 异步编程
- **serde**: 序列化/反序列化
- **tracing**: 日志记录

### 模块架构

```
src/
├── main.rs                 # 应用程序入口点
├── application.rs          # 应用程序主类
├── window/                 # 主窗口模块
│   ├── mod.rs             # 窗口管理
│   ├── progress_icon.rs   # 进度图标
│   └── toggle_button.rs   # 切换按钮
├── recording.rs           # 录制核心逻辑
├── pipeline.rs            # GStreamer 管道构建
├── screencast_portal/     # 屏幕录制门户接口
│   ├── mod.rs            # 门户主模块
│   ├── types.rs          # 类型定义
│   ├── handle_token.rs   # 句柄令牌
│   ├── variant_dict.rs   # 变体字典
│   └── window_identifier.rs # 窗口标识符
├── area_selector/         # 区域选择器
│   ├── mod.rs            # 选择器主模块
│   └── view_port.rs      # 视口管理
├── profile.rs            # 编码配置文件
├── settings.rs           # 应用设置
├── preferences_dialog.rs # 偏好设置对话框
├── device.rs             # 设备管理
├── format.rs             # 格式处理
├── timer.rs              # 计时器
├── experimental.rs       # 实验性功能
├── about.rs              # 关于对话框
├── help.rs               # 帮助功能
├── cancelled.rs          # 取消操作处理
├── item_row.rs           # 列表项行
├── i18n.rs               # 国际化
└── config.rs.in          # 配置模板
```

## 核心模块流程

### 1. 应用程序启动流程

应用程序的启动过程遵循标准的 GTK4 应用程序模式：

1. **初始化阶段**
   - 设置日志系统 (tracing)
   - 配置国际化 (gettext)
   - 初始化 GStreamer 框架
   - 注册自定义 GStreamer 插件

2. **资源加载**
   - 加载 GResource 资源包
   - 注册应用程序图标
   - 设置应用程序元数据

3. **应用程序创建**
   - 创建 Application 实例
   - 设置应用程序 ID 和基础路径
   - 注册全局操作和快捷键

4. **窗口管理**
   - 创建主窗口实例
   - 加载 UI 模板
   - 初始化设置管理器

### 2. 录制核心流程

录制功能是 Kooha 的核心，涉及多个系统组件的协调工作：

#### 2.1 权限和会话管理
- 通过 XDG Desktop Portal 请求屏幕录制权限
- 创建 Screencast 会话并获取会话句柄
- 处理用户的源选择（显示器、窗口或区域）

#### 2.2 媒体管道构建
- 根据用户设置构建 GStreamer 管道
- 配置视频编码器参数（分辨率、帧率、质量）
- 设置音频编码器（如果启用音频录制）
- 连接复用器和文件输出

#### 2.3 实时处理
- 从 PipeWire 接收视频帧数据
- 处理音频流（桌面音频和/或麦克风）
- 实时编码和写入文件
- 更新用户界面状态

### 3. 详细的 GStreamer 管道架构

GStreamer 管道是音视频处理的核心，采用模块化设计：

#### 3.1 视频处理链
```
pipewiresrc → videoscale → videocrop → videoconvert → queue → encoder → muxer
```

- **pipewiresrc**: 从 PipeWire 获取视频流
- **videoscale**: 缩放视频到目标分辨率
- **videocrop**: 裁剪选定区域（区域录制模式）
- **videoconvert**: 颜色空间转换和格式标准化
- **queue**: 缓冲区管理，平滑数据流
- **encoder**: 视频编码（VP8/VP9/AV1/x264/硬件编码器）

#### 3.2 音频处理链
```
audiosrc → audioconvert → encoder → queue → muxer
```

- **audiosrc**: 音频源（PulseAudio/PipeWire）
- **audioconvert**: 音频格式转换
- **encoder**: 音频编码（Opus/MP3/AAC）
- **queue**: 音频缓冲区

#### 3.3 输出处理
- **muxer**: 将音视频流复用到容器格式
- **filesink**: 写入到目标文件

## 构建系统

### Meson 构建配置

项目使用 Meson 作为主要构建系统，配合 Cargo 进行 Rust 代码编译。

#### 主要构建文件
- `meson.build`: 主构建配置
- `src/meson.build`: Rust 代码构建配置
- `data/meson.build`: 资源文件构建配置
- `po/meson.build`: 国际化构建配置

#### 构建依赖
```bash
# 系统依赖
meson >= 0.59
ninja
cargo
appstreamcli
glib-compile-resources
glib-compile-schemas

# 库依赖
glib-2.0 >= 2.80
gio-2.0 >= 2.80
gtk4 >= 4.15.3
libadwaita-1 >= 1.6
gstreamer-1.0 >= 1.24
gstreamer-plugins-base-1.0 >= 1.24
```

#### 构建配置选项
- `profile`: 构建配置文件 (default/development)
- 开发模式: 启用调试信息和实验性功能
- 发布模式: 优化编译，启用 LTO

### Flatpak 打包

项目提供完整的 Flatpak 打包支持，包括：

#### 开发版本配置
- **文件**: `build-aux/io.github.seadve.Kooha.Devel.json`
- **运行时**: org.gnome.Platform (master)
- **SDK**: org.gnome.Sdk + Rust 扩展

#### 权限配置
```json
"finish-args": [
    "--device=dri",              // GPU 访问
    "--filesystem=xdg-videos",   // 视频目录访问
    "--share=ipc",               // IPC 共享
    "--socket=fallback-x11",     // X11 支持
    "--socket=pulseaudio",       // 音频访问
    "--socket=wayland"           // Wayland 支持
]
```

## 开发环境搭建

### 系统要求

#### 运行时依赖
- **PipeWire**: 现代音视频服务器
- **gstreamer-plugin-pipewire**: PipeWire 的 GStreamer 插件
- **xdg-desktop-portal**: 桌面门户服务
- **xdg-desktop-portal-***: 特定桌面环境的门户实现
  - GNOME: xdg-desktop-portal-gnome
  - KDE: xdg-desktop-portal-kde
  - wlroots: xdg-desktop-portal-wlr

#### 开发依赖
- **Rust** (2024 edition): 主要编程语言
- **Meson** (>= 0.59): 构建系统
- **Ninja**: 构建工具
- **Cargo**: Rust 包管理器
- **pkg-config**: 库依赖检测

### 使用 GNOME Builder (推荐)

GNOME Builder 提供了完整的 Flatpak 开发环境：

1. **安装 GNOME Builder**
   ```bash
   flatpak install flathub org.gnome.Builder
   ```

2. **克隆项目**
   - 在 Builder 中点击 "Clone Repository"
   - 输入 URL: `https://github.com/SeaDve/Kooha.git`

3. **构建项目**
   - Builder 会自动检测 Flatpak 清单
   - 点击顶部的构建按钮
   - 首次构建会下载所有依赖

4. **运行和调试**
   - 使用 Builder 的集成调试器
   - 支持断点和变量检查

### 使用 Meson 手动构建

适合系统级开发和打包：

```bash
# 1. 克隆项目
git clone https://github.com/SeaDve/Kooha.git
cd Kooha

# 2. 安装系统依赖 (以 Fedora 为例)
sudo dnf install meson ninja-build cargo rust \
    gtk4-devel libadwaita-devel gstreamer1-devel \
    gstreamer1-plugins-base-devel glib2-devel

# 3. 配置构建
meson setup _build --prefix=/usr/local

# 4. 编译
ninja -C _build

# 5. 安装
sudo ninja -C _build install
```

### 开发模式构建

开发模式提供额外的调试功能：

```bash
# 开发模式配置
meson setup _build --prefix=/usr/local -Dprofile=development

# 编译开发版本
ninja -C _build

# 运行测试套件
ninja -C _build test

# 运行代码检查
ninja -C _build cargo-clippy
```

### 使用 Flatpak 开发

推荐的开发方式，提供一致的环境：

```bash
# 1. 安装 Flatpak 和开发工具
sudo dnf install flatpak flatpak-builder

# 2. 添加 Flathub 仓库
flatpak remote-add --if-not-exists flathub https://flathub.org/repo/flathub.flatpakrepo

# 3. 安装 GNOME 运行时
flatpak install flathub org.gnome.Platform//master org.gnome.Sdk//master

# 4. 构建开发版本
flatpak-builder --user --install --force-clean \
    _build build-aux/io.github.seadve.Kooha.Devel.json

# 5. 运行开发版本
flatpak run io.github.seadve.Kooha.Devel
```

## 发布流程

### 版本管理策略

Kooha 采用语义化版本控制 (SemVer)：

- **主版本号**: 不兼容的 API 变更
- **次版本号**: 向后兼容的功能性新增
- **修订号**: 向后兼容的问题修正

### 详细发布步骤

#### 1. 准备发布

```bash
# 1. 创建发布分支
git checkout -b release/v2.3.2

# 2. 更新版本号
# 编辑 meson.build
version: '2.3.2'

# 编辑 Cargo.toml
version = "2.3.2"

# 3. 更新变更日志
# 编辑 src/about.rs 中的 release_notes()
```

#### 2. 测试验证

```bash
# 运行完整测试套件
ninja -C _build test

# 代码质量检查
ninja -C _build cargo-clippy

# Flatpak 构建测试
flatpak-builder --user --install --force-clean \
    _build build-aux/io.github.seadve.Kooha.Devel.json

# 功能测试
flatpak run io.github.seadve.Kooha.Devel
```

#### 3. 创建发布

```bash
# 1. 提交变更
git add .
git commit -m "Release v2.3.2"

# 2. 创建标签
git tag -a v2.3.2 -m "Release v2.3.2"

# 3. 推送到远程
git push origin release/v2.3.2
git push origin v2.3.2
```

#### 4. Flathub 发布

1. **更新 Flathub 清单**
   - Fork flathub/io.github.seadve.Kooha 仓库
   - 更新 `io.github.seadve.Kooha.json` 中的版本和源码 URL
   - 更新依赖版本和校验和

2. **提交 Pull Request**
   - 创建 PR 到 Flathub 仓库
   - 等待自动构建验证
   - 响应审核反馈

3. **发布确认**
   - PR 合并后自动发布到 Flathub
   - 验证新版本可用性

### CI/CD 流程详解

#### GitHub Actions 工作流

项目使用多个 GitHub Actions 工作流：

1. **持续集成 (.github/workflows/ci.yml)**
   ```yaml
   name: CI
   on: [push, pull_request]
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - name: Setup Rust
           uses: actions-rs/toolchain@v1
         - name: Run tests
           run: cargo test
         - name: Run clippy
           run: cargo clippy -- -D warnings
   ```

2. **Flatpak 构建验证**
   - 使用官方 Flatpak GitHub Actions
   - 验证清单文件正确性
   - 确保构建成功

3. **自动化发布**
   - 标签推送触发发布流程
   - 生成发布说明
   - 创建 GitHub Release

#### 质量保证流程

- **代码审查**: 所有 PR 需要审查
- **自动化测试**: 单元测试覆盖率 > 80%
- **静态分析**: Clippy 检查无警告
- **安全扫描**: 依赖漏洞检查
- **性能测试**: 内存和 CPU 使用监控

## 配置和设置

### 应用设置 (GSettings)

主要配置项存储在 GSettings 中：

```xml
<key name="capture-mode" type="s">
  <choices>
    <choice value="monitor-window"/>
    <choice value="selection"/>
  </choices>
</key>
<key name="record-desktop-audio" type="b"/>
<key name="record-microphone" type="b"/>
<key name="show-pointer" type="b"/>
<key name="record-delay" type="u"/>
<key name="saving-location" type="ay"/>
<key name="profile-id" type="s"/>
<key name="framerate" type="(ii)"/>
```

### 编码配置文件

编码配置存储在 `data/resources/profiles.yml` 中：

- **支持的格式**: WebM (VP8)、MP4、GIF、Matroska
- **实验性格式**: WebM (VP9)、WebM (AV1)、硬件加速编码器

### 实验性功能

通过环境变量 `KOOHA_EXPERIMENTAL` 启用：

- `all`: 启用所有实验性功能
- `experimental-formats`: 实验性编码格式
- `multiple-video-sources`: 多视频源录制
- `window-recording`: 窗口录制

## 国际化支持

项目支持多语言本地化：

- **翻译系统**: GNU gettext
- **翻译平台**: Weblate
- **支持语言**: 40+ 种语言
- **翻译文件**: `po/` 目录下的 `.po` 文件

## 故障排除

### 系统兼容性检查

#### 1. 桌面环境支持

| 桌面环境 | 支持状态 | 所需组件 | 备注 |
|---------|---------|----------|------|
| GNOME | ✅ 完全支持 | xdg-desktop-portal-gnome | 推荐环境 |
| KDE Plasma | ✅ 完全支持 | xdg-desktop-portal-kde | 良好支持 |
| Sway/wlroots | ✅ 支持 | xdg-desktop-portal-wlr | 需要配置 |
| XFCE | ⚠️ 部分支持 | xdg-desktop-portal-gtk | 功能受限 |
| i3/其他 | ⚠️ 部分支持 | xdg-desktop-portal-wlr | 需要手动配置 |

#### 2. 音视频系统检查

```bash
# 检查 PipeWire 状态
systemctl --user status pipewire
systemctl --user status pipewire-pulse

# 检查 GStreamer 插件
gst-inspect-1.0 pipewiresrc
gst-inspect-1.0 x264enc
gst-inspect-1.0 vp8enc

# 检查 Portal 服务
systemctl --user status xdg-desktop-portal
```

### 常见问题诊断

#### 1. 录制功能不工作

**症状**: 点击录制按钮无响应或出现错误

**诊断步骤**:
```bash
# 1. 检查 Portal 服务
dbus-send --session --print-reply \
    --dest=org.freedesktop.portal.Desktop \
    /org/freedesktop/portal/desktop \
    org.freedesktop.portal.ScreenCast.CreateSession

# 2. 检查权限
flatpak permission-show io.github.seadve.Kooha

# 3. 查看系统日志
journalctl --user -u xdg-desktop-portal -f
```

**解决方案**:
- 安装对应桌面环境的 Portal 实现
- 重启 Portal 服务: `systemctl --user restart xdg-desktop-portal`
- 检查 Flatpak 权限设置

#### 2. 音频录制问题

**症状**: 视频录制正常但无音频

**诊断步骤**:
```bash
# 检查音频设备
pactl list sources short
pw-cli list-objects | grep -A5 -B5 "media.class.*Audio/Source"

# 测试音频录制
gst-launch-1.0 pulsesrc ! audioconvert ! vorbisenc ! oggmux ! filesink location=test.ogg
```

**解决方案**:
- 确保 PipeWire 或 PulseAudio 正常运行
- 检查应用程序音频权限
- 验证音频设备可用性

#### 3. 编码性能问题

**症状**: 录制过程中丢帧或卡顿

**诊断工具**:
```bash
# 监控系统资源
htop
nvidia-smi  # NVIDIA GPU
radeontop   # AMD GPU

# GStreamer 调试
GST_DEBUG=3 kooha 2>&1 | grep -i "warning\|error"
```

**优化建议**:
- 降低录制分辨率和帧率
- 使用硬件编码器（如果可用）
- 调整编码质量设置
- 关闭不必要的后台程序

### 高级调试

#### 1. 详细日志记录

```bash
# 完整调试日志
RUST_LOG=debug RUST_BACKTRACE=1 kooha

# GStreamer 管道调试
GST_DEBUG=4 GST_DEBUG_FILE=/tmp/gst.log kooha

# Flatpak 环境调试
flatpak run --devel --command=sh io.github.seadve.Kooha
```

#### 2. 性能分析

```bash
# 使用 perf 分析性能
perf record -g kooha
perf report

# 内存使用分析
valgrind --tool=massif kooha
```

#### 3. 网络问题诊断

```bash
# 检查网络连接（用于在线功能）
curl -I https://flathub.org
ping -c 4 github.com

# 代理设置检查
echo $http_proxy $https_proxy
```

### 开发调试技巧

#### 1. 使用 GDB 调试

```bash
# 编译调试版本
meson setup _build -Dprofile=development
ninja -C _build

# GDB 调试
gdb _build/src/kooha
(gdb) set environment RUST_BACKTRACE=1
(gdb) run
```

#### 2. Rust 特定调试

```bash
# 启用 Rust 回溯
export RUST_BACKTRACE=full

# 使用 cargo 调试工具
cargo install cargo-flamegraph
cargo flamegraph --bin kooha

# 内存泄漏检测
cargo install cargo-valgrind
cargo valgrind run
```

#### 3. GTK 调试

```bash
# GTK 调试信息
GTK_DEBUG=interactive kooha

# 检查 UI 文件
gtk4-builder-tool validate data/resources/ui/window.ui
```

## 贡献指南

### 代码贡献流程

#### 1. 准备开发环境

```bash
# Fork 项目到个人账户
# 克隆 Fork 的仓库
git clone https://github.com/YOUR_USERNAME/Kooha.git
cd Kooha

# 添加上游仓库
git remote add upstream https://github.com/SeaDve/Kooha.git

# 设置开发环境
meson setup _build -Dprofile=development
```

#### 2. 开发工作流

```bash
# 1. 同步上游更新
git fetch upstream
git checkout main
git merge upstream/main

# 2. 创建功能分支
git checkout -b feature/your-feature-name

# 3. 进行开发
# 编辑代码...

# 4. 运行测试
ninja -C _build test
ninja -C _build cargo-clippy

# 5. 提交变更
git add .
git commit -m "feat: add your feature description"

# 6. 推送分支
git push origin feature/your-feature-name
```

#### 3. Pull Request 规范

- **标题格式**: `type(scope): description`
  - `feat`: 新功能
  - `fix`: 错误修复
  - `docs`: 文档更新
  - `style`: 代码格式化
  - `refactor`: 代码重构
  - `test`: 测试相关
  - `chore`: 构建/工具相关

- **描述要求**:
  - 清晰描述变更内容
  - 说明变更原因
  - 列出测试步骤
  - 附上相关 Issue 链接

#### 4. 代码规范

```rust
// Rust 代码风格
use std::collections::HashMap;

pub struct ExampleStruct {
    field_name: String,
}

impl ExampleStruct {
    pub fn new(name: String) -> Self {
        Self {
            field_name: name,
        }
    }

    pub fn method_name(&self) -> &str {
        &self.field_name
    }
}
```

### 翻译贡献

#### 1. 加入翻译团队

访问 [Weblate 平台](https://hosted.weblate.org/engage/seadve/) 参与翻译：

1. 注册 Weblate 账户
2. 选择目标语言
3. 开始翻译工作

#### 2. 翻译指南

- **保持一致性**: 使用统一的术语
- **上下文理解**: 理解字符串的使用场景
- **格式保留**: 保持占位符和格式标记
- **简洁明了**: 翻译应简洁易懂

#### 3. 本地化测试

```bash
# 更新翻译文件
ninja -C _build kooha-update-po

# 测试特定语言
LANG=zh_CN.UTF-8 kooha
```

### 文档贡献

#### 1. 文档类型

- **用户文档**: README、使用指南
- **开发文档**: API 文档、架构说明
- **维护文档**: 发布流程、故障排除

#### 2. 文档规范

- 使用 Markdown 格式
- 包含代码示例
- 提供截图说明
- 保持更新及时

### 社区参与

#### 1. 问题报告

使用 GitHub Issues 报告问题：

- **Bug 报告**: 详细描述问题和复现步骤
- **功能请求**: 说明需求和使用场景
- **问题讨论**: 参与技术讨论

#### 2. 社区支持

- **GitHub Discussions**: 技术讨论和问答
- **Matrix 聊天室**: 实时交流
- **邮件列表**: 开发公告

## 性能优化建议

### 录制性能优化

#### 1. 硬件加速

```bash
# 启用实验性硬件编码
KOOHA_EXPERIMENTAL=experimental-formats kooha

# 检查可用的硬件编码器
gst-inspect-1.0 | grep -i "va\|nvenc\|qsv"
```

#### 2. 编码参数调优

- **降低质量设置**: 减少 CPU 使用
- **调整帧率**: 30fps vs 60fps
- **选择合适格式**: WebM 比 MP4 更高效

#### 3. 系统优化

```bash
# 设置 CPU 性能模式
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 增加文件描述符限制
ulimit -n 4096

# 优化 I/O 调度
echo mq-deadline | sudo tee /sys/block/sda/queue/scheduler
```

### 内存使用优化

- 定期清理临时文件
- 监控内存使用情况
- 避免长时间录制大文件

## 安全考虑

### 权限管理

Kooha 遵循最小权限原则：

- **屏幕访问**: 仅在录制时请求
- **文件系统**: 限制在用户目录
- **网络访问**: 无网络权限需求

### 隐私保护

- 录制内容完全本地处理
- 不收集用户数据
- 支持加密存储（用户自行配置）

## 路线图和未来计划

### 短期目标 (v2.4)

- [ ] 改进硬件编码支持
- [ ] 优化内存使用
- [ ] 增强错误处理
- [ ] 更多编码格式支持

### 中期目标 (v3.0)

- [ ] 重构 UI 架构
- [ ] 插件系统
- [ ] 云存储集成
- [ ] 实时流媒体支持

### 长期目标

- [ ] 跨平台支持 (Windows/macOS)
- [ ] AI 功能集成
- [ ] 协作录制功能

---

## 致谢

感谢所有为 Kooha 项目做出贡献的开发者、翻译者和用户。特别感谢：

- **核心开发团队**: Dave Patrick Caberto 及贡献者
- **翻译社区**: 40+ 种语言的翻译志愿者
- **测试用户**: 提供反馈和错误报告的用户
- **开源项目**: GStreamer、GTK、LibAdwaita 等基础库

## 许可证

本项目采用 GPL-3.0-or-later 许可证。详细信息请参阅 [COPYING](COPYING) 文件。

---

*本文档基于 Kooha 2.3.1 版本编写，最后更新时间：2024年12月。如有更新请参考项目官方仓库：https://github.com/SeaDve/Kooha*
