{"id": "io.github.seadve.Kooha.Devel", "runtime": "org.gnome.Platform", "runtime-version": "master", "sdk": "org.gnome.Sdk", "sdk-extensions": ["org.freedesktop.Sdk.Extension.rust-stable", "org.freedesktop.Sdk.Extension.llvm20"], "command": "kooha", "finish-args": ["--device=dri", "--filesystem=xdg-videos", "--share=ipc", "--socket=fallback-x11", "--socket=pulseaudio", "--socket=wayland", "--env=RUST_BACKTRACE=1", "--env=RUST_LIB_BACKTRACE=0", "--env=RUST_LOG=kooha=debug", "--env=G_MESSAGES_DEBUG=none", "--env=KOOHA_EXPERIMENTAL=all"], "build-options": {"append-path": "/usr/lib/sdk/llvm20/bin:/usr/lib/sdk/rust-stable/bin", "build-args": ["--share=network"], "env": {"CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_LINKER": "clang", "CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_RUSTFLAGS": "-C link-arg=-fuse-ld=/usr/lib/sdk/rust-stable/bin/mold"}}, "modules": [{"name": "x264", "config-opts": ["--enable-shared", "--enable-pic", "--disable-cli"], "sources": [{"type": "git", "url": "https://code.videolan.org/videolan/x264.git", "branch": "stable", "commit": "31e19f92f00c7003fa115047ce50978bc98c3a0d"}]}, {"name": "gst-plugins-ugly", "buildsystem": "meson", "builddir": true, "config-opts": ["-Ddoc=disabled", "-Dnls=disabled", "-Dtests=disabled", "-Dgpl=enabled"], "sources": [{"type": "archive", "url": "https://gstreamer.freedesktop.org/src/gst-plugins-ugly/gst-plugins-ugly-1.26.5.tar.xz", "sha256": "3dfc43435be97e110816bac6d602b0f206a038546279683d9d25372ff127db52"}]}, {"name": "kooha", "buildsystem": "meson", "run-tests": true, "config-opts": ["-Dprofile=development"], "sources": [{"type": "dir", "path": "../"}]}]}