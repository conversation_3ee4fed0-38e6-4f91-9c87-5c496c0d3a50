---
name: Bug report
about: Create a bug report to help <PERSON><PERSON><PERSON> improve.
title: ''
labels: ''
assignees: ''
---

**Affected version**

<!--
Open <PERSON><PERSON><PERSON>'s Main Menu, click on About <PERSON><PERSON><PERSON>, go to
Troubleshooting > Debugging Information, copy the text, and paste it here.

If there are other relevant version information, please include them here.
-->

**Bug summary**

<!--
Provide a short summary of the bug you encountered.
-->

**Steps to reproduce**

<!--
1. Go to '...'
2. Click on '....'
3. See error
-->

**Expected behavior**

<!--
What did you expect <PERSON><PERSON><PERSON> should do?
-->

**Relevant logs, screenshots, screencasts, etc.**

<!--
If you have further information, such as technical documentation, logs,
screenshots or screencasts related, please provide them here.

If applicable, please attach the logs from running <PERSON>oh<PERSON> in the
terminal with the following environment variables: `RUST_BACKTRACE=1 RUST_LOG=kooha=debug GST_DEBUG=3 PIPEWIRE_DEBUG=3`
(e.g., `RUST_BACKTRACE=1 RUST_LOG=kooha=debug GST_DEBUG=3 PIPEWIRE_DEBUG=3 flatpak run io.github.seadve.<PERSON>oha`).
-->
