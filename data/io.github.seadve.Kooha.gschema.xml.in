<?xml version="1.0" encoding="utf-8"?>
<schemalist>
  <schema path="/io/github/seadve/Kooha/" id="@app-id@" gettext-domain="@gettext-package@">
    <key type="s" name="capture-mode">
      <choices>
        <choice value="monitor-window"/>
        <choice value="selection"/>
      </choices>
      <default>"monitor-window"</default>
    </key>
    <key type="b" name="record-desktop-audio">
      <default>true</default>
    </key>
    <key type="b" name="record-microphone">
      <default>false</default>
    </key>
    <key type="b" name="show-pointer">
      <default>true</default>
    </key>
    <key type="u" name="record-delay">
      <default>0</default>
    </key>
    <key type="ay" name="saving-location">
      <default>b""</default>
    </key>
    <key type="s" name="profile-id">
      <default>"webm-vp8"</default>
    </key>
    <key type="(ii)" name="framerate">
      <default>(30, 1)</default>
    </key>
    <key type="s" name="screencast-restore-token">
      <default>""</default>
    </key>
    <key type="(dddd)" name="selection">
      <default>(-1, -1, -1, -1)</default>
    </key>
    <key type="((dddd)(ii))" name="selection-context">
      <default>((-1, -1, -1, -1), (-1, -1))</default>
    </key>
  </schema>
</schemalist>
