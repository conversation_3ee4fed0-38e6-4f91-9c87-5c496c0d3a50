<?xml version="1.0" encoding="UTF-8"?>
<gresources>
  <gresource prefix="/io/github/seadve/Kooha/">
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/audio-volume-high-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/audio-volume-muted-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/checkmark-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/microphone-disabled-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/microphone2-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/mouse-wireless-disabled-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/mouse-wireless-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/refresh-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/selection-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/source-pick-symbolic.svg</file>
    <file compressed="true" preprocess="xml-stripblanks">icons/scalable/actions/warning-symbolic.svg</file>
    <file compressed="true">profiles.yml</file>
    <file compressed="true">style.css</file>
    <file compressed="true" preprocess="xml-stripblanks">ui/area_selector.ui</file>
    <file compressed="true" preprocess="xml-stripblanks">ui/item_row.ui</file>
    <file compressed="true" preprocess="xml-stripblanks">ui/preferences_dialog.ui</file>
    <file compressed="true" preprocess="xml-stripblanks" alias="gtk/help-overlay.ui">ui/shortcuts.ui</file>
    <file compressed="true" preprocess="xml-stripblanks">ui/view_port.ui</file>
    <file compressed="true" preprocess="xml-stripblanks">ui/window.ui</file>
  </gresource>
</gresources>
