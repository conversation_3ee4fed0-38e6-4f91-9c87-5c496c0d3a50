<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <requires lib="gtk" version="4.0"/>
  <requires lib="libadwaita" version="1.0"/>
  <template class="KoohaPreferencesDialog" parent="AdwPreferencesDialog">
    <child>
      <object class="AdwPreferencesPage">
        <child>
          <object class="AdwPreferencesGroup">
            <property name="title" translatable="yes">General</property>
            <child>
              <object class="AdwSpinRow" id="delay_row">
                <property name="title" translatable="yes">Delay (Seconds)</property>
                <property name="subtitle" translatable="yes">Time interval before recording begins</property>
                <property name="adjustment">
                  <object class="GtkAdjustment">
                    <property name="lower">0</property>
                    <property name="upper">10</property>
                    <property name="step-increment">1</property>
                    <property name="page-increment">5</property>
                  </object>
                </property>
              </object>
            </child>
            <child>
              <object class="AdwActionRow">
                <property name="title" translatable="yes">Recordings Folder</property>
                <property name="subtitle" translatable="yes">Destination folder for the recordings</property>
                <property name="action-name">preferences.select-saving-location</property>
                <property name="activatable">True</property>
                <child type="suffix">
                  <object class="GtkLabel" id="file_chooser_label">
                    <property name="xalign">0</property>
                    <property name="ellipsize">middle</property>
                    <property name="max-width-chars">30</property>
                  </object>
                </child>
                <child type="suffix">
                  <object class="GtkImage">
                    <property name="valign">center</property>
                    <property name="icon-name">go-next-symbolic</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="AdwPreferencesGroup">
            <property name="title" translatable="yes">Video</property>
            <child>
              <object class="AdwComboRow" id="profile_row">
                <property name="title" translatable="yes">Format</property>
              </object>
            </child>
            <child>
              <object class="AdwComboRow" id="framerate_row">
                <property name="title" translatable="yes">Frame Rate</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </template>
</interface>
