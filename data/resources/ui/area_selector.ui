<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <requires lib="gtk" version="4.0"/>
  <requires lib="libadwaita" version="1.0"/>
  <template class="KoohaAreaSelector" parent="AdwWindow">
    <property name="default-widget">done_button</property>
    <property name="content">
      <object class="AdwToolbarView">
        <child type="top">
          <object class="AdwHeaderBar">
            <property name="show-start-title-buttons">False</property>
            <property name="show-end-title-buttons">False</property>
            <property name="title-widget">
              <object class="AdwWindowTitle" id="window_title">
                <property name="title" translatable="yes">Select Area</property>
              </object>
            </property>
            <child>
              <object class="GtkButton">
                <property name="label" translatable="yes">Cancel</property>
                <property name="action-name">area-selector.cancel</property>
              </object>
            </child>
            <child type="end">
              <object class="GtkButton" id="done_button">
                <property name="label" translatable="yes">Done</property>
                <property name="action-name">area-selector.done</property>
                <style>
                  <class name="suggested-action"/>
                </style>
              </object>
            </child>
            <child type="end">
              <object class="GtkButton">
                <property name="tooltip-text" translatable="yes">Reset Selection</property>
                <property name="action-name">area-selector.reset</property>
                <property name="icon-name">refresh-symbolic</property>
              </object>
            </child>
          </object>
        </child>
        <property name="content">
          <object class="GtkStack" id="stack">
            <property name="transition-type">crossfade</property>
            <child>
              <object class="GtkBox" id="loading">
                <property name="valign">center</property>
                <property name="orientation">vertical</property>
                <property name="spacing">24</property>
                <child>
                  <object class="AdwSpinner">
                    <property name="height-request">24</property>
                    <property name="width-request">24</property>
                  </object>
                </child>
                <child>
                  <object class="GtkLabel">
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="justify">center</property>
                    <property name="label">Loading…</property>
                    <style>
                      <class name="title-1"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="KoohaViewPort" id="view_port">
                <property name="vexpand">True</property>
                <style>
                  <class name="view-port"/>
                </style>
              </object>
            </child>
          </object>
        </property>
      </object>
    </property>
    <style>
      <class name="area-selector"/>
    </style>
  </template>
</interface>
