# Note:
# - videoenc and audioenc will be connected directly to the muxer
# - audioenc and muxer are optional, but if audioenc is set, muxer must also be set
# - ${N_THREADS} will be replaced with ideal thread count
# - default suggested-max-fps is 60

supported:
  - id: webm-vp8
    name: WebM
    extension: webm
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      vp8enc max-quantizer=17 cpu-used=16 deadline=1 static-threshold=100 keyframe-mode=disabled buffer-size=20000 threads=${N_THREADS} !
      queue
    audioenc: >
      audioconvert !
      opusenc !
      queue
    muxer: webmmux

  - id: mp4
    name: MP4
    extension: mp4
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      x264enc qp-max=17 speed-preset=ultrafast threads=${N_THREADS} !
      capsfilter caps=video/x-h264,profile=baseline !
      queue !
      h264parse
    audioenc: >
      audioconvert !
      lamemp3enc !
      queue !
      mpegaudioparse
    muxer: mp4mux fragment-duration=500 fragment-mode=first-moov-then-finalise

  - id: matroska-h264
    name: Matroska
    extension: mkv
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      x264enc qp-max=17 speed-preset=ultrafast threads=${N_THREADS} !
      capsfilter caps=video/x-h264,profile=baseline !
      queue !
      h264parse
    audioenc: >
      audioconvert !
      opusenc !
      queue
    muxer: matroskamux

  - id: gif
    name: GIF
    extension: gif
    suggested-max-fps: 24
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      gifenc repeat=-1 speed=30 !
      queue

experimental:
  - id: webm-vp9
    name: WebM (VP9)
    extension: webm
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      vp9enc max-quantizer=17 cpu-used=16 deadline=1 static-threshold=100 keyframe-mode=disabled buffer-size=20000 threads=${N_THREADS} !
      queue
    audioenc: >
      audioconvert !
      opusenc !
      queue
    muxer: webmmux

  - id: webm-av1
    name: WebM (AV1)
    extension: webm
    videoenc: >
      videoconvert chroma-mode=none dither=none matrix-mode=output-only n-threads=${N_THREADS} !
      av1enc usage-profile=realtime max-quantizer=17 cpu-used=5 end-usage=cq buf-sz=20000 threads=${N_THREADS} !
      queue
    audioenc: >
      audioconvert !
      opusenc !
      queue
    muxer: webmmux

  - id: va-h264
    name: WebM VA H264
    extension: mp4
    videoenc: >
      vapostproc !
      vah264enc !
      queue !
      h264parse
    audioenc: >
      audioconvert !
      lamemp3enc !
      queue !
      mpegaudioparse
    muxer: mp4mux fragment-duration=500 fragment-mode=first-moov-then-finalise
