row.error-view {
  padding: 0px;
}

label.large-time {
  font-size: 450%;
  font-weight: 300;
  font-feature-settings: "tnum";
}

label.recording {
  color: var(--destructive-bg-color);
}

label.paused {
  animation-name: blinking;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(1.0, 0, 0, 1.0);
  animation-duration: 1s;
}

button.copy-done {
  color: var(--accent-bg-color);
}

window.area-selector .view-port {
  padding: 12px;
  padding-top: 6px;
}

@keyframes blinking {
  0% {
    color: var(--window-fg-color);
  }
}
