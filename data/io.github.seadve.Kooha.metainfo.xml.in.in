<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2024 Dave <PERSON> -->
<component type="desktop-application">
  <id>@app-id@</id>
  <metadata_license>CC0-1.0</metadata_license>
  <project_license>GPL-3.0-or-later</project_license>
  <name><PERSON><PERSON>a</name>
  <summary>Elegantly record your screen</summary>
  <description>
    <p>Capture your screen in a intuitive and straightforward way without distractions.</p>
    <p><PERSON><PERSON><PERSON> is a simple screen recorder with a minimalist interface. You can just click the record
      button without having to configure a bunch of settings.</p>
    <p>The main features of Kooha include the following:</p>
    <ul>
      <li>🎙️ Record microphone, desktop audio, or both at the same time</li>
      <li>📼 Support for WebM, MP4, GIF, and Matroska formats</li>
      <li>🖥️ Select a monitor or a portion of the screen to record</li>
      <li>🛠️ Configurable saving location, pointer visibility, frame rate, and delay</li>
      <li>🚀 Experimental hardware accelerated encoding</li>
    </ul>
  </description>
  <screenshots>
    <screenshot type="default">
      <image>https://raw.githubusercontent.com/SeaDve/Kooha/main/data/screenshots/screenshot.png</image>
      <caption>Recording options and button to start recording</caption>
    </screenshot>
    <screenshot>
      <image>https://raw.githubusercontent.com/SeaDve/Kooha/main/data/screenshots/screenshot2.png</image>
      <caption>In-progress recording duration and button to stop recording</caption>
    </screenshot>
    <screenshot>
      <image>https://raw.githubusercontent.com/SeaDve/Kooha/main/data/screenshots/screenshot3.png</image>
      <caption>Countdown to start recording and button to cancel recording</caption>
    </screenshot>
  </screenshots>
  <url type="bugtracker">https://github.com/SeaDve/Kooha/issues</url>
  <url type="homepage">https://github.com/SeaDve/Kooha</url>
  <url type="donation">https://seadve.github.io/donate/</url>
  <url type="translate">https://hosted.weblate.org/projects/seadve/kooha/</url>
  <url type="vcs-browser">https://github.com/SeaDve/Kooha</url>
  <content_rating type="oars-1.1"/>
  <releases>
    <release version="2.3.1" date="2025-08-23">
      <description translate="no">
        <p>This release contains minor changes:</p>
        <ul>
          <li>Updated UI</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.3.0" date="2024-03-21">
      <description translate="no">
        <p>This release contains new features and fixes:</p>
        <ul>
          <li>Area selector window is now resizable</li>
          <li>Previous selected area is now remembered</li>
          <li>Logout and idle are now inhibited while recording</li>
          <li>Video format and FPS are now shown in the main view</li>
          <li>Notifications now show the duration and size of the recording</li>
          <li>Notification actions now work even when the application is closed</li>
          <li>Progress is now shown when flushing the recording</li>
          <li>It is now much easier to pick from frame rate options</li>
          <li>Actually fixed audio from stuttering and being cut on long recordings</li>
          <li>Record audio in stereo rather than mono when possible</li>
          <li>Recordings are no longer deleted when flushing is cancelled</li>
          <li>Significant improvements in recording performance</li>
          <li>Improved preferences dialog UI</li>
          <li>Fixed incorrect output video orientation on certain compositors</li>
          <li>Fixed incorrect focus on area selector</li>
          <li>Fixed too small area selector window default size on HiDPI monitors</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.2.4" date="2023-09-23">
      <description translate="no">
        <p>This release contains minor fixes:</p>
        <ul>
          <li>Added window close shortcut</li>
          <li>Improve file saving handling (thanks to @DaPigGuy)</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.2.3" date="2022-12-25">
      <description translate="no">
        <p>This release contains minor fixes:</p>
        <ul>
          <li>Fixed indefinite flushing time on certain distros</li>
          <li>Slightly improved performance on certain configurations</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.2.2" date="2022-10-02">
      <description translate="no">
        <p>This release contains minor fixes:</p>
        <ul>
          <li>Fixed error dialog showing when show uri is simply cancelled</li>
          <li>Fixed more audio issues</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.2.1" date="2022-10-01">
      <description translate="no">
        <p>This release contains minor fixes:</p>
        <ul>
          <li>Only show None profile when it is active</li>
          <li>Guard window selection behind `KOOHA_EXPERIMENTAL` env var</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.2.0" date="2022-09-29">
      <description translate="no">
        <p>This release contains new features and fixes:</p>
        <ul>
          <li>New area selection UI</li>
          <li>Added option to change the frame rate through the UI</li>
          <li>Improved delay settings flexibility</li>
          <li>Added preferences window for easier configuration</li>
          <li>Added `KOOHA_EXPERIMENTAL` env var to show experimental (unsupported) encoders like
            VAAPI-VP8 and VAAPI-H264</li>
          <li>Added the following experimental (unsupported) encoders: VP9, AV1, and VAAPI-VP9</li>
          <li>Unavailable formats/encoders are now hidden from the UI</li>
          <li>Fixed broken audio on long recordings</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.1.1" date="2022-08-21">
      <description translate="no">
        <p>This release contains fixes:</p>
        <ul>
          <li>Improved tooltip text on settings toggle buttons</li>
          <li>Fallback to manual mode when failed to get default device name through GStreamer
            device monitor</li>
        </ul>
      </description>
    </release>
    <release version="2.1.0" date="2022-08-19">
      <description translate="no">
        <p>This release contains new features and fixes:</p>
        <ul>
          <li>Remember previously selected video sources</li>
          <li>Added ability to cancel while flushing the recording</li>
          <li>Use different icons for settings toggle button to discern state more clearly</li>
          <li>Added 3 seconds delay option</li>
          <li>Fixed x264 encoder failing to initialize on uneven resolutions</li>
          <li>Fixed minutes stuck on 00 if time is equal or greater than an hour</li>
          <li>Recordings are now stored by default in `~/Videos/Kooha` (This won't affect existing
            settings)</li>
          <li>"Show in Files" button in notifications now highlights the file in the file manager</li>
          <li>Improved support information in the new about window</li>
          <li>Improved error handling</li>
          <li>Improved codebase and stability</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="2.0.1" date="2021-10-20">
      <description translate="no">
        <p>This is a minor release:</p>
        <ul>
          <li>GIF recordings now loops infinitely</li>
          <li>Minor codebase improvements</li>
          <li>Added Serbian Cyrillic translations</li>
          <li>Added Serbian Latin translations</li>
          <li>Updated Croatian translations</li>
          <li>Updated Ukrainian translations</li>
          <li>Updated Basque translations</li>
          <li>Updated Dutch translations</li>
          <li>Updated Turkish translations</li>
          <li>Updated Italian translations</li>
          <li>Updated Spanish translations</li>
          <li>Updated Hungarian translations</li>
          <li>Updated German translations</li>
          <li>Updated Slovak translations</li>
        </ul>
      </description>
    </release>
    <release version="2.0.0" date="2021-09-23">
      <description translate="no">
        <p>This is a big release containing a lot of fixes and new features!</p>
        <ul>
          <li>Added MP4 and GIF formats</li>
          <li>Added feature to record a single window or monitor</li>
          <li>Added optional-in hardware accelerated encoding</li>
          <li>Added multiple video sources recording</li>
          <li>Added much improved error handling</li>
          <li>Fixed broken UI on non-default close button placement</li>
          <li>Fixed major bug in GNOME 41</li>
          <li>Fixed off selection region on integer scaling</li>
          <li>Fixed saving the recording to a hidden directory</li>
          <li>Improved performance</li>
          <li>Now working for certain desktop environments and window managers</li>
          <li>Full rewrite to Rust</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="1.2.1" date="2021-05-23">
      <description translate="no">
        <p>This release contains small fixes and improvements:</p>
        <ul>
          <li>Focus start record button on startup</li>
          <li>Improved UI and UX</li>
          <li>Fixed bug when tmp folder doesn't exist</li>
          <li>Updated Swedish translations</li>
          <li>Updated Spanish translations</li>
          <li>Updated Turkish translations</li>
          <li>Updated Italian translations</li>
          <li>Updated Dutch translations</li>
          <li>Updated French translations</li>
          <li>Updated Portuguese (Brazil) translations</li>
          <li>Updated Norwegian Bokmål translations</li>
        </ul>
      </description>
    </release>
    <release version="1.2.0" date="2021-05-17">
      <description translate="no">
        <p>This release brings the new GTK4 and an important fix for GNOME 40.</p>
        <ul>
          <li>Recording performance improvements</li>
          <li>Major UI improvements</li>
          <li>Dynamically change the multiplexer</li>
          <li>Fix GNOME 40 crashes</li>
          <li>Fix crashes in non-supported environment</li>
          <li>Updated Spanish translations</li>
          <li>Updated Portuguese translations</li>
          <li>Updated Turkish translations</li>
          <li>Updated Norwegian Bokmål translations</li>
          <li>Updated Italian translations</li>
          <li>Updated Indonesian translations</li>
        </ul>
      </description>
    </release>
    <release version="1.1.3" date="2021-05-04">
      <description translate="no">
        <p>New fixes and translations update coming this release:</p>
        <ul>
          <li>More responsive shortcuts</li>
          <li>Fix crashing when no desktop environment is set up</li>
          <li>Updated Slovak Translations</li>
          <li>Updated Portuguese (Brazil) translations</li>
          <li>Updated Russian translations</li>
          <li>Updated Indonesian translations</li>
        </ul>
      </description>
    </release>
    <release version="1.1.2" date="2021-04-25">
      <description translate="no">
        <p>This is a minor release composing of fixes and improvements:</p>
        <ul>
          <li>Minor performance optimizations</li>
          <li>Updated Dutch translations</li>
          <li>Updated Turkish translations</li>
          <li>Updated Spanish translations</li>
          <li>Updated Indonesian translations</li>
          <li>Updated Filipino translations</li>
          <li>Updated Italian translations</li>
          <li>Updated French translations</li>
          <li>Updated German translations</li>
          <li>Updated Norwegian Bokmål translations</li>
          <li>Updated Chinese(simplified) translations</li>
        </ul>
      </description>
    </release>
    <release version="1.1.1" date="2021-04-09">
      <description translate="no">
        <p>This release is composing of minor changes:</p>
        <ul>
          <li>Minor UI improvements</li>
          <li>Code cleaning and minor performance improvements</li>
          <li>Fixed a bug when trying to save in an inaccessible location</li>
          <li>Added Persian translations</li>
          <li>Added Chinese(simplified) translations</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="1.1.0" date="2021-03-25">
      <description translate="no">
        <p>This update brings new features and few bug fixes:</p>
        <ul>
          <li>Added a processing view to avoid recording corruption</li>
          <li>Added a notification after recording to show saving location</li>
          <li>Added option to change framerate through Kooha's keyfile</li>
          <li>Minor improvements</li>
        </ul>
      </description>
    </release>
    <release version="1.0.5" date="2021-03-24">
      <description translate="no">
        <p>This is a small update composing of bug fixes:</p>
        <ul>
          <li>Fix audio recording in other locales</li>
          <li>Fix when using keyboard shortcuts unexpectedly</li>
          <li>Minor improvements</li>
          <li>Update translations</li>
        </ul>
      </description>
    </release>
    <release version="1.0.4" date="2021-03-23">
      <description translate="no">
        <p>This release brings a couple of improvements:</p>
        <ul>
          <li>New! Ability to select audio devices through GNOME Settings</li>
          <li>Removed the annoying tong sound</li>
          <li>Fixed when the selection is not divisible by 2</li>
          <li>Minor UI tweaks and other bug fixes</li>
          <li>Performance improvements</li>
          <li>Added new translations</li>
          <li>Updated translations</li>
        </ul>
      </description>
    </release>
    <release version="1.0.3" date="2021-03-17">
      <description translate="no">
        <p>A couple of bug fixes here and improvements there:</p>
        <ul>
          <li>Minor performance improvements</li>
          <li>Bug fixes</li>
          <li>Added Swedish translations</li>
          <li>Added Indonesian translations</li>
          <li>Update Filipino translations</li>
        </ul>
      </description>
    </release>
    <release version="1.0.2" date="2021-03-15">
      <description translate="no">
        <p>This release brings a couple of small changes:</p>
        <ul>
          <li>set WebM as default format</li>
          <li>Minor fixes</li>
          <li>Add Filipino translations</li>
        </ul>
      </description>
    </release>
    <release version="1.0.1" date="2021-03-14">
      <description translate="no">
        <p>Version 1.0.1 brings fixes to a couple of bugs:</p>
        <ul>
          <li>Catch the error when the path of the directory no longer exists</li>
          <li>Fix the bug when the path contains spaces</li>
          <li>Fix missing argument argv</li>
          <li>Possible fix for GioDBus: The name is not activatable</li>
          <li>Make desktop environment check less strict</li>
        </ul>
      </description>
    </release>
    <release version="1.0.0" date="2021-03-13">
      <description translate="no">
        <p>This is the 1.0 release of Kooha. The features of this release include the following:</p>
        <ul>
          <li>Record your screen and also audio from your microphone or desktop</li>
          <li>Option to show or hide the pointer</li>
          <li>Option to record only an area on your screen</li>
          <li>Select the saving location of the record</li>
          <li>Ability to add a delay before recording starts</li>
          <li>Video format settings (MKV and WebM)</li>
          <li>And of course, keyboard shortcuts!</li>
        </ul>
      </description>
    </release>
    <release version="0.1.0" date="2021-03-06">
      <description translate="no">
        <p>Initial release.</p>
      </description>
    </release>
  </releases>
  <!-- developer_name tag deprecated with Appstream 1.0 -->
  <developer_name translate="no">Dave Patrick Caberto</developer_name>
  <developer id="io.github.seadve">
    <name translate="no">Dave Patrick Caberto</name>
  </developer>
  <update_contact><EMAIL></update_contact>
  <translation type="gettext">@gettext-package@</translation>
  <launchable type="desktop-id">@app-id@.desktop</launchable>
  <recommends>
    <control>keyboard</control>
    <control>pointing</control>
    <control>touch</control>
  </recommends>
</component>
