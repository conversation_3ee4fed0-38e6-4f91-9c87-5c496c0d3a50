<?xml version="1.0" encoding="UTF-8"?>
<svg height="128px" viewBox="0 0 128 128" width="128px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <linearGradient id="a" gradientUnits="userSpaceOnUse" x1="7.999874" x2="119.999874" y1="116.000092" y2="116.000092">
        <stop offset="0" stop-color="#77767b"/>
        <stop offset="0.036" stop-color="#c0bfbc"/>
        <stop offset="0.071" stop-color="#9a9996"/>
        <stop offset="0.929" stop-color="#9a9996"/>
        <stop offset="0.964" stop-color="#c0bfbc"/>
        <stop offset="1" stop-color="#77767b"/>
    </linearGradient>
    <linearGradient id="b" gradientTransform="matrix(-1 0 0 1 6.14387 -113.311909)" gradientUnits="userSpaceOnUse" x1="-132.348587" x2="-41.352005" y1="119.487427" y2="193.889999">
        <stop offset="0" stop-color="#bd49d2"/>
        <stop offset="1" stop-color="#f33f4a"/>
    </linearGradient>
    <filter id="c" height="100%" width="100%" x="0%" y="0%">
        <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
    </filter>
    <mask id="d">
        <g filter="url(#c)">
            <rect fill-opacity="0.5" height="128" width="128"/>
        </g>
    </mask>
    <clipPath id="e">
        <rect height="152" width="192"/>
    </clipPath>
    <path d="m 16 40 h 96 c 4.417969 0 8 3.582031 8 8 v 60 c 0 4.417969 -3.582031 8 -8 8 h -96 c -4.417969 0 -8 -3.582031 -8 -8 v -60 c 0 -4.417969 3.582031 -8 8 -8 z m 0 0" fill="url(#a)"/>
    <path d="m 16 28 h 96 c 4.417969 0 8 3.582031 8 8 v 68 c 0 4.417969 -3.582031 8 -8 8 h -96 c -4.417969 0 -8 -3.582031 -8 -8 v -68 c 0 -4.417969 3.582031 -8 8 -8 z m 0 0" fill="#deddda"/>
    <path d="m 16 32 h 96 c 2.210938 0 4 1.789062 4 4 v 68 c 0 2.210938 -1.789062 4 -4 4 h -96 c -2.210938 0 -4 -1.789062 -4 -4 v -68 c 0 -2.210938 1.789062 -4 4 -4 z m 0 0" fill="#241f31"/>
    <path d="m 112 36 h -96 v 68 h 96 z m 0 0" fill="url(#b)"/>
    <g clip-path="url(#e)" mask="url(#d)" transform="matrix(1 0 0 1 -8 -16)">
        <path d="m 19 221 v -10 h 10 m 80.015625 10 v -10 h -10 m 10 52.015625 v 10 h -10 m -80.015625 -10 v 10 h 10" fill="none" stroke="#ffffff" stroke-linecap="round" stroke-width="2" transform="matrix(1 0 0 1 8 -156)"/>
    </g>
    <g fill="#ffffff">
        <path d="m 78.640625 59.421875 h 0.003906 l -9.976562 0.007813 c -6.546875 0 -10.015625 -0.003907 -10.015625 -0.003907 c -0.921875 0 -1.65625 0.890625 -1.679688 1.761719 c 0 0 -0.03125 -0.71875 -0.03125 8.75 l 0.007813 8.828125 c 0.121093 1.300781 0.894531 1.742187 1.722656 1.8125 l 10.007813 -0.023437 h 9.898437 c 1.417969 0 1.851563 -0.953126 1.925781 -1.839844 v -8.773438 l -0.003906 -8.550781 c 0 -1.234375 -0.796875 -1.96875 -1.859375 -1.96875 z m -31.144531 2.34375 v 16.453125 h 0.484375 l 0.828125 -0.011719 l 3.742187 -4.078125 c 2.246094 -2.25 4.085938 -4.148437 4.085938 -4.222656 c 0 -0.070312 -1.800781 -1.929688 -4.003907 -4.132812 l -3.65625 -4 l -0.914062 -0.007813 z m 21.234375 0.601563 c 0.304687 0 0.609375 0.019531 0.914062 0.054687 c 4.171875 0.507813 7.152344 4.289063 6.667969 8.464844 l -0.015625 0.136719 c -0.5625 4.164062 -4.382813 7.09375 -8.554687 6.554687 c -3.972657 -0.542969 -6.851563 -4.0625 -6.589844 -8.066406 c 0.257812 -4 3.566406 -7.121094 7.578125 -7.144531 z m 0 0"/>
        <path d="m 72.722656 70.542969 c -0.296875 2.195312 -2.316406 3.742187 -4.515625 3.457031 c -2.199219 -0.289062 -3.75 -2.296875 -3.476562 -4.496094 c 0.277343 -2.203125 2.28125 -3.765625 4.480469 -3.5 c 2.203124 0.265625 3.777343 2.261719 3.519531 4.464844"/>
    </g>
</svg>
