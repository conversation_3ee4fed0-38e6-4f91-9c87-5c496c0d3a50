[package]
name = "kooha"
version = "2.3.1"
authors = ["<PERSON> <dave<PERSON><PERSON><EMAIL>>"]
license = "GPL-3.0-or-later"
edition = "2024"

[profile.release]
lto = true

# Don't compile and link debug info; these reduce build times at the
# cost of not having line numbers in backtraces.
[profile.dev]
debug = 0
strip = "debuginfo"

[dependencies]
adw = { package = "libadwaita", version = "0.8", features = ["v1_6"] }
anyhow = "1.0.59"
futures-channel = "0.3.19"
futures-util = { version = "0.3", default-features = false }
gdk-wayland = { package = "gdk4-wayland", version = "0.10" }
gdk-x11 = { package = "gdk4-x11", version = "0.10" }
gettext-rs = { version = "0.7.0", features = ["gettext-system"] }
gsettings-macro = "0.2"
gst = { package = "gstreamer", version = "0.24", features = ["v1_20"] }
gst-plugin-gif = "0.14"
gst-plugin-gtk4 = { version = "0.14", features = [
    "dmabuf",
    "gtk_v4_14",
    "wayland",
    "x11egl",
    "x11glx",
] }
gtk = { package = "gtk4", version = "0.10", features = ["gnome_46"] }
once_cell = "1.19.0"
serde_yaml = "0.9.31"
serde = { version = "1.0.196", features = ["derive"] }
tracing = "0.1.36"
tracing-subscriber = "0.3.15"
